using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Consts;
using TikTok.DateTimes;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using TikTok.TikTokApiClients;
using TikTokBusinessApi;
using TikTokBusinessApi.Models;
using Volo.Abp;
using Volo.Abp.Uow;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu báo cáo chi tiết cấp creative GMV Max Product Campaign
    /// </summary>
    public class GmvMaxProductCreativeSyncService : BaseSyncService, IGmvMaxProductCreativeSyncService
    {
        private readonly IRawGmvMaxProductCreativeReportRepository _gmvMaxProductCreativeReportRepository;
        private readonly IAssetRepository _assetRepository;
        private readonly IRawGmvMaxCampaignsRepository _gmvMaxCampaignsRepository;
        private readonly ITikTokApiClientService _tikTokApiClientService;

        public GmvMaxProductCreativeSyncService(
            IServiceProvider serviceProvider,
            IRawGmvMaxProductCreativeReportRepository gmvMaxProductCreativeReportRepository,
            ILogger<GmvMaxProductCreativeSyncService> logger,
            IAssetRepository assetRepository,
            IRawGmvMaxCampaignsRepository gmvMaxCampaignsRepository,
            ITikTokApiClientService tikTokApiClientService) : base(serviceProvider, logger)
        {
            _gmvMaxProductCreativeReportRepository = gmvMaxProductCreativeReportRepository;
            _assetRepository = assetRepository;
            _gmvMaxCampaignsRepository = gmvMaxCampaignsRepository;
            _tikTokApiClientService = tikTokApiClientService;
        }

        /// <summary>
        /// Đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxProductCreativeSyncResult> SyncAllGmvMaxProductCreativeForAllBcsAsync()
        {
            var result = new GmvMaxProductCreativeSyncResult
            {
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign cho tất cả BC");

                var allBcs = await _businessApplicationCache.GetAllActiveAsync();
                var bcIds = allBcs.Select(x => x.BcId).Distinct().ToList();

                var totalResult = new GmvMaxProductCreativeSyncResult
                {
                };

                foreach (var bcId in bcIds)
                {
                    var bcResult = await SyncGmvMaxProductCreativeAsync(bcId);
                    totalResult.TotalSynced += bcResult.TotalSynced;
                    totalResult.NewRecords += bcResult.NewRecords;
                    totalResult.UpdatedRecords += bcResult.UpdatedRecords;
                    totalResult.DayCount += bcResult.DayCount;
                    totalResult.CampaignCount += bcResult.CampaignCount;
                    totalResult.StoreCount += bcResult.StoreCount;
                    totalResult.ProductCount += bcResult.ProductCount;
                    totalResult.CreativeCount += bcResult.CreativeCount;
                    totalResult.BcCount++;
                }

                result = totalResult;

                _logger.LogDebug("Hoàn thành đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign cho tất cả BC. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, BC: {BcCount}, Campaign: {CampaignCount}, Store: {StoreCount}, Product: {ProductCount}, Creative: {CreativeCount}, Ngày: {DayCount}",
                    result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.BcCount, result.CampaignCount, result.StoreCount, result.ProductCount, result.CreativeCount, result.DayCount);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign cho tất cả BC");
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign cho tất cả BC");
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxProductCreativeSyncResult> SyncGmvMaxProductCreativeAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var jobStartTime = DateTime.UtcNow;
            var apiCallCount = 0L;
            var result = new GmvMaxProductCreativeSyncResult
            {
                BcCount = 1
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign cho BC: {BcId}", bcId);

                // Tạo TikTok client từ Singleton
                var tikTokClient = await _tikTokApiClientService.GetOrCreateClientAsync(bcId);

                string bcTimezone = DateTimeService.UTC_TIMEZONE;

                var bc = await _businessCenterCache.GetByBcIdAsync(bcId);
                if (bc != null && !string.IsNullOrEmpty(bc.Timezone))
                {
                    bcTimezone = bc.Timezone;
                }

                var advertisers = await _assetRepository.GetByBcIdAsync(bcId, assetType: Enums.AssetType.ADVERTISER);
                var advertiserIds = advertisers.Select(x => x.AssetId).ToList();

                // Duyệt từng nhà quảng cáo
                foreach (var advertiserId in advertiserIds)
                {
                    (DateTime startDate, DateTime endDate) rangeDateFiltering;
                    if (startDate.HasValue && endDate.HasValue)
                    {
                        if(startDate.Value > endDate.Value)
                        {
                            throw new UserFriendlyException("Ngày bắt đầu phải nhỏ hơn ngày kết thúc");
                        }
                        rangeDateFiltering = (startDate.Value, endDate.Value);
                    }
                    else
                    {
                        rangeDateFiltering = await GetRangeDateFiltering(bcId, advertiserId, bcTimezone);
                    }

                    var currentDate = rangeDateFiltering.startDate.Date;
                    var end = rangeDateFiltering.endDate.Date;

                    while (currentDate <= end)
                    {
                        _logger.LogDebug("Đồng bộ dữ liệu báo cáo chi tiết cấp creative GMV Max Product Campaign cho Advertiser: {AdvertiserId}, Ngày: {Date}", advertiserId, currentDate.ToString("yyyy-MM-dd"));

                        // Count API calls before making the request (regardless of success/failure)
                        var gmvMaxCampaigns = await _gmvMaxCampaignsRepository.GetByAdvertiserIdAsync(advertiserId);
                        var itemGroupCount = gmvMaxCampaigns
                            .Where(x => x.ItemGroupIds != null)
                            .SelectMany(x => x.ItemGroupIds)
                            .Distinct()
                            .Count();
                        apiCallCount += itemGroupCount; // Each item group requires one API call

                        var apiResponse = await GetSyncGmvMaxProductCreativeFromApiAsync(tikTokClient, bcId, advertiserId, currentDate, currentDate);
                        if (apiResponse != null && apiResponse.Any())
                        {
                            await ProcessReportDataAsync(bcId, advertiserId, bcTimezone, apiResponse, result, currentDate);
                        }
                        else
                        {
                            _logger.LogDebug("Không có dữ liệu báo cáo chi tiết cấp creative GMV Max Product Campaign cho Advertiser: {AdvertiserId}, Ngày: {Date}", advertiserId, currentDate.ToString("yyyy-MM-dd"));
                        }
                        currentDate = currentDate.AddDays(1);
                    }
                }

                _logger.LogDebug("Hoàn thành đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign cho BC: {BcId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, Campaign: {CampaignCount}, Store: {StoreCount}, Product: {ProductCount}, Creative: {CreativeCount}, Ngày: {DayCount}",
                    bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.CampaignCount, result.StoreCount, result.ProductCount, result.CreativeCount, result.DayCount);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign cho BC: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign cho BC: {BcId}", bcId);
            }

            // Set API call count in result
            result.ApiCallCount = apiCallCount;

            // Log job metrics for this BC
            LogJobMetrics($"SyncGmvMaxProductCreative-{bcId}", jobStartTime, apiCallCount, new Dictionary<string, object>
            {
                ["BcId"] = bcId,
                ["TotalSynced"] = result.TotalSynced,
                ["NewRecords"] = result.NewRecords,
                ["UpdatedRecords"] = result.UpdatedRecords,
                ["CampaignCount"] = result.CampaignCount,
                ["StoreCount"] = result.StoreCount,
                ["ProductCount"] = result.ProductCount,
                ["CreativeCount"] = result.CreativeCount,
                ["DayCount"] = result.DayCount
            });

            return result;
        }

        private async Task<(DateTime startDate, DateTime endDate)> GetRangeDateFiltering(string bcId, string advertiserId, string timezone)
        {
            // Lấy ngày hiện tại theo timezone của BC
            DateTime currentDateInTimezone = _dateTimeService.GetDateNow(timezone);
            DateTime endDate = currentDateInTimezone;

            // Lấy dữ liệu báo cáo chi tiết cấp creative GMV Max Product Campaign mới nhất (theo giờ)
            //var latestReport = await GetLatestReportByBcIdAsync(bcId, advertiserId);

            DateTime startDate= currentDateInTimezone;
            //if (latestReport == null)
            //{
            //    // Nếu chưa có dữ liệu trong DB thì lấy khoảng 1 tuần từ ngày hiện tại
            //    startDate = currentDateInTimezone.AddDays(-LAST_SYNC_DAYS);
            //    _logger.LogDebug("Chưa có dữ liệu báo cáo chi tiết cấp creative GMV Max Product Campaign trong DB cho BC: {BcId}, Advertiser: {AdvertiserId}. Lấy dữ liệu 1 tuần từ {StartDate} đến {EndDate}",
            //        bcId, advertiserId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));
            //}
            //else
            //{
            //    // Nếu có dữ liệu trong DB thì lấy từ ngày trong DB đến ngày hiện tại
            //    // Convert từ UTC (trong DB) sang timezone của BC để so sánh
            //    var latestReportDateInTimezone = _dateTimeService.ConvertFromUtc(latestReport.Date, timezone).Date;
            //    startDate = latestReportDateInTimezone;
            //    _logger.LogDebug("Có dữ liệu báo cáo chi tiết cấp creative GMV Max Product Campaign trong DB cho BC: {BcId}, Advertiser: {AdvertiserId}. Lấy từ {StartDate} đến {EndDate}",
            //        bcId, advertiserId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));
            //}

            return (startDate, endDate);
        }

        /// <summary>
        /// Lấy báo cáo mới nhất theo BC ID và advertiserId
        /// </summary>
        /// <param name="bcId">BC ID</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <returns>Báo cáo mới nhất</returns>
        private async Task<RawGmvMaxProductCreativeReportEntity?> GetLatestReportByBcIdAsync(string bcId, string advertiserId)
        {
            var query = await _gmvMaxProductCreativeReportRepository.GetLatestByBcIdAndAdvertiserIdAsync(bcId, advertiserId);

            return query;
        }

        /// <summary>
        /// Lấy dữ liệu báo cáo chi tiết cấp creative GMV Max Product Campaign từ TikTok API
        /// </summary>
        private async Task<List<GmvMaxProductCreativeReportData>> GetSyncGmvMaxProductCreativeFromApiAsync(TikTokBusinessApiClient tikTokClient, string bcId, string advertiserId, DateTime startDate, DateTime endDate)
        {
            var records = new List<GmvMaxProductCreativeReportData>();

            // Lấy danh sách StoreId và ItemGroupIds từ GMV Max Campaigns theo AdvertiserId
            var gmvMaxCampaigns = await _gmvMaxCampaignsRepository.GetByAdvertiserIdAsync(advertiserId);
            var storeItemGroupMapping = gmvMaxCampaigns
                .Where(x => !string.IsNullOrEmpty(x.StoreId) && x.ItemGroupIds != null)
                .GroupBy(x => new { x.StoreId, x.CampaignId })
                .Select(g => new
                {
                    g.Key.StoreId,
                    g.Key.CampaignId,
                    ItemGroupIds = g.SelectMany(x => x.ItemGroupIds).Distinct().ToList()
                })
                .ToList();

            if (!storeItemGroupMapping.Any())
            {
                _logger.LogDebug("Không có Store và ItemGroup ID nào cho Advertiser: {AdvertiserId}", advertiserId);
                return records;
            }

            _logger.LogDebug("Tìm thấy {Count} Store-ItemGroup mapping cho Advertiser: {AdvertiserId}", storeItemGroupMapping.Count, advertiserId);

            // Duyệt từng Store-ItemGroup mapping
            foreach (var mapping in storeItemGroupMapping)
            {
                // Duyệt từng ItemGroupId
                foreach (var itemGroupId in mapping.ItemGroupIds)
                {
                    // Gọi API 2 lần: lần 1 cho ORGANIC, lần 2 cho ADS_AND_ORGANIC
                    //var organicRecords = await GetReportFromApiByCreativeTypeAsync(tikTokClient, advertiserId, mapping.StoreId, mapping.CampaignId, itemGroupId, "ORGANIC");
                    var adsAndOrganicRecords = await GetReportFromApiAsync(tikTokClient, advertiserId, mapping.StoreId, mapping.CampaignId, itemGroupId, startDate, endDate);
                    //foreach (var item in organicRecords)
                    //{
                    //    item.CreativeType = Enums.CreativeType.ORGANIC;
                    //}

                    //foreach (var item in adsAndOrganicRecords)
                    //{
                    //    item.CreativeType = Enums.CreativeType.ADS_AND_ORGANIC;
                    //}

                    //records.AddRange(organicRecords);
                    records.AddRange(adsAndOrganicRecords);
                }
            }

            return records;
        }

        /// <summary>
        /// Lấy báo cáo từ API theo creative type
        /// </summary>
        private async Task<List<GmvMaxProductCreativeReportData>> GetReportFromApiAsync(TikTokBusinessApiClient tikTokClient, string advertiserId, string storeId, string campaignId, string itemGroupId,DateTime startDate,DateTime endDate, string creativeType=null)
        {
            var records = new List<GmvMaxProductCreativeReportData>();
            var page = 1;
            const int pageSize = PAGE_SIZE_SYNC_REPORT;

            var filtering = new GMVMaxReportFiltering
            {
                CampaignIds = new List<string> { campaignId },
                ItemGroupIds = new List<string> { itemGroupId }, // Từ campaign data
            };
            if (!creativeType.IsNullOrWhiteSpace())
            {
                filtering.CreativeTypes = new List<string> { creativeType };
            }
            while (true)
            {
                var request = new GMVMaxReportRequest
                {
                    AdvertiserId = advertiserId,
                    StoreIds = new List<string> { storeId },
                    StartDate = startDate.ToString("yyyy-MM-dd"), // Lấy dữ liệu 1 ngày trước
                    EndDate = endDate.ToString("yyyy-MM-dd"),
                    Dimensions = new List<string> { "item_id" }, // Chỉ item_id
                    Metrics = new List<string>
                    {
                        "currency", "title", "tt_account_name", "tt_account_profile_image_url",
                        "tt_account_authorization_type", "shop_content_type","creative_delivery_status",
                        "creative_type", "orders", "gross_revenue", "product_impressions", "product_clicks",
                        "product_click_rate", "ad_click_rate", "ad_conversion_rate",
                        "ad_video_view_rate_2s", "ad_video_view_rate_6s", "ad_video_view_rate_p25",
                        "ad_video_view_rate_p50", "ad_video_view_rate_p75", "ad_video_view_rate_p100",
                        "roi", "cost_per_order", "cost"
                    },
                    Filtering = filtering,
                    Page = page,
                    PageSize = pageSize
                };

                var response = await MeasureApiCallAsync(
                    () => tikTokClient.GMVMax.GetReportAsync(request),
                    "GMVMax.GetReport",
                    ("advertiser_id", advertiserId),
                    ("store_id", storeId),
                    ("campaign_id", campaignId),
                    ("item_group_id", itemGroupId),
                    ("report_type", "product_creative"),
                    ("creative_type", creativeType ?? "all"),
                    ("page", page)
                );

                if (!TikTokApiCodes.IsSuccess(response.Code))
                {
                    throw new BusinessException(response.Code.ToString(), $"Lỗi khi lấy dữ liệu báo cáo chi tiết cấp creative GMV Max Product Campaign cho CreativeType {creativeType}: {response.Message}");
                }

                if (response?.Data?.List == null || !response.Data.List.Any())
                {
                    break;
                }

                // Thêm AdvertiserId, StoreId và CampaignId vào mỗi item
                foreach (var item in response.Data.List)
                {
                    records.Add(new GmvMaxProductCreativeReportData(item, advertiserId, storeId, campaignId, itemGroupId));
                }

                // Kiểm tra xem còn trang tiếp theo không
                if (response.Data.PageInfo?.TotalPage <= page)
                {
                    break;
                }

                page++;
            }

            _logger.LogDebug("Lấy được {Count} records cho CreativeType {CreativeType}, ItemGroupId {ItemGroupId}, CampaignId {CampaignId}",
                records.Count, creativeType, itemGroupId, campaignId);

            return records;
        }

        /// <summary>
        /// Xử lý dữ liệu báo cáo từ API
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="advertiserId">ID nhà quảng cáo</param>
        /// <param name="bcTimezone">Timezone của Business Center</param>
        /// <param name="reportDataList">Danh sách dữ liệu báo cáo</param>
        /// <param name="result">Kết quả đồng bộ</param>
        /// <param name="syncDate">Ngày đồng bộ</param>
        private async Task ProcessReportDataAsync(string bcId, string advertiserId, string bcTimezone, List<GmvMaxProductCreativeReportData> reportDataList, GmvMaxProductCreativeSyncResult result, DateTime syncDate)
        {
            // sử dụng UnitOfWork để đảm bảo tính toàn vẹn dữ liệu cho mỗi 200 bản ghi
            if (reportDataList == null || !reportDataList.Any())
            {
                _logger.LogDebug("Không có dữ liệu báo cáo chi tiết cấp creative GMV Max Product Campaign để xử lý cho {Total} bản ghi", reportDataList?.Count ?? 0);
                return;
            }

            var pageSize = PAGE_SIZE_HANDLE_SAVE_TO_DATABASE;
            var totalPages = (int)Math.Ceiling((double)reportDataList.Count / pageSize);
            _logger.LogDebug("Bắt đầu xử lý {TotalRecords} bản ghi báo cáo chi tiết cấp creative GMV Max Product Campaign cho {TotalPages} trang", reportDataList.Count, totalPages);

            for (int page = 0; page < totalPages; page++)
            {
                var pageData = reportDataList.Skip(page * pageSize).Take(pageSize).ToList();
                if (pageData.Any())
                {
                    try
                    {
                        await ProcessPageDataAsync(bcId, bcTimezone, pageData, result, syncDate);
                    }
                    catch (BusinessException ex)
                    {
                        _logger.LogError(ex, "Lỗi khi xử lý dữ liệu báo cáo chi tiết cấp creative GMV Max Product Campaign cho {Total} bản ghi, Trang: {Page}", pageData.Count, page + 1);
                        result.ErrorMessage += ex.Message + Environment.NewLine;
                        result.Code = TikTokApiCodes.PartialSuccess.ToString();
                        continue; // Bỏ qua lỗi và tiếp tục với trang tiếp theo
                    }
                }
            }
        }

        private async Task ProcessPageDataAsync(string bcId, string bcTimezone, List<GmvMaxProductCreativeReportData> pageData, GmvMaxProductCreativeSyncResult result, DateTime syncDate)
        {
            // use unit of work
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
            {
                var mappedEntities = await MapListReportDataToEntitiesAsync(bcId, bcTimezone, pageData, syncDate);

                var existingEntities = await GetExistingEntitiesAsync(bcId, mappedEntities);

                var insertedEntities = new List<RawGmvMaxProductCreativeReportEntity>();
                var updatedEntities = new List<RawGmvMaxProductCreativeReportEntity>();

                foreach (var mappedEntity in mappedEntities)
                {
                    // So sánh theo CampaignId, ItemGroupId, ItemId và Date
                    var currentEntity = existingEntities.FirstOrDefault(x => x.CampaignId == mappedEntity.CampaignId &&
                    x.ItemGroupId == mappedEntity.ItemGroupId &&
                    x.ItemId == mappedEntity.ItemId &&
                    x.StoreId == mappedEntity.StoreId&&
                    x.Date.Date == mappedEntity.Date.Date);
                    if (currentEntity == null)
                    {
                        insertedEntities.Add(mappedEntity);
                        result.NewRecords++;
                    }
                    else
                    {
                        // Cập nhật nếu có thay đổi thì tạo mới
                        if (currentEntity.IsDifferentFrom(mappedEntity, false))
                        {
                            insertedEntities.Add(mappedEntity);
                            result.NewRecords++;
                            if (currentEntity.StatusChanged(mappedEntity, false))
                            {
                                // todo: add noti
                            }
                        }
                    }
                }

                // Thêm các bản ghi mới vào kho dữ liệu
                if (insertedEntities.Any())
                {
                    await _gmvMaxProductCreativeReportRepository.InsertManyAsync(insertedEntities);
                }
                // Cập nhật các bản ghi đã tồn tại
                if (updatedEntities.Any())
                {
                    await _gmvMaxProductCreativeReportRepository.UpdateManyAsync(updatedEntities);
                }

                result.DayCount += pageData.Count;
                result.CampaignCount += pageData.Select(x => x.CampaignId).Distinct().Count();
                result.StoreCount += pageData.Select(x => x.StoreId).Distinct().Count();
                result.ProductCount += pageData.Select(x => x.ReportItem.Dimensions?.GetValueOrDefault("item_group_id")?.ToString()).Distinct().Count();
                result.CreativeCount += pageData.Select(x => x.ReportItem.Dimensions?.GetValueOrDefault("item_id")?.ToString()).Distinct().Count();

                await uow.CompleteAsync();
            }
        }

        /// <summary>
        /// Lấy các entity hiện có trong database
        /// </summary>
        private async Task<List<RawGmvMaxProductCreativeReportEntity>> GetExistingEntitiesAsync(string bcId, List<RawGmvMaxProductCreativeReportEntity> mappedEntities)
        {
            if (!mappedEntities.Any())
                return new List<RawGmvMaxProductCreativeReportEntity>();

            var campaignIds = mappedEntities.Select(x => x.CampaignId).Distinct().ToList();
            var advertiserIds = mappedEntities.Select(x => x.AdvertiserId).Distinct().ToList();
            var itemGroupIds = mappedEntities.Select(x => x.ItemGroupId).Distinct().ToList();
            var query = await _gmvMaxProductCreativeReportRepository.GetLatestByCampaignIdsAndAdvertiserIdsAndItemGroupIdsAsync(
                campaignIds: campaignIds,
                advertiserIds: advertiserIds,
                itemGroupIds: itemGroupIds);

            // Lấy ra bản ghi mới nhất dựa vào trường Date
            return query;
        }

        private async Task<List<RawGmvMaxProductCreativeReportEntity>> MapListReportDataToEntitiesAsync(string bcId, string bcTimezone, List<GmvMaxProductCreativeReportData> reportDataList, DateTime syncDate)
        {
            var entities = new List<RawGmvMaxProductCreativeReportEntity>();

            foreach (var reportData in reportDataList)
            {
                var entity = MapReportDataToEntity(bcId, bcTimezone, reportData, syncDate);
                if (entity != null)
                {
                    entities.Add(entity);
                }
            }
            return entities;
        }

        /// <summary>
        /// Map dữ liệu báo cáo từ API sang entity
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="bcTimezone">Timezone của Business Center</param>
        /// <param name="reportData">Dữ liệu báo cáo</param>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="storeId">ID của Store</param>
        /// <param name="campaignId">ID của Campaign</param>
        /// <param name="itemGroupId">ID của Item Group</param>
        /// <param name="syncDate">Ngày đồng bộ</param>
        /// <returns>RawGmvMaxProductCreativeReportEntity</returns>
        private RawGmvMaxProductCreativeReportEntity? MapReportDataToEntity(string bcId, string bcTimezone, GmvMaxProductCreativeReportData productReportData, DateTime syncDate)
        {
            var reportData = productReportData.ReportItem;
            string advertiserId = productReportData.AdvertiserId;
            string storeId = productReportData.StoreId;
            string campaignId = productReportData.CampaignId;
            string itemGroupId = productReportData.ItemGroupId;

            var itemId = reportData.Dimensions?.GetValueOrDefault("item_id")?.ToString();
            if (string.IsNullOrEmpty(itemId))
            {
                _logger.LogWarning("Item ID không hợp lệ cho BC: {BcId}", bcId);
                return null;
            }

            // Convert syncDate từ timezone của BC sang UTC
            var syncDateUtc = _dateTimeService.ConvertToUtc(syncDate, bcTimezone);

            var entity = new RawGmvMaxProductCreativeReportEntity(Guid.NewGuid())
            {
                BcId = bcId,
                AdvertiserId = advertiserId,
                StoreId = storeId,
                CampaignId = campaignId,
                ItemGroupId = itemGroupId,
                ItemId = itemId,
                Date = syncDateUtc,  // Lưu datetime UTC
                Currency = "USD", // Default currency
                CreativeType = productReportData.CreativeType
            };

            if (reportData.Metrics != null)
            {
                // Map các metrics từ API response
                entity.Title = GetStringValue(reportData.Metrics, "title");
                entity.TtAccountName = GetStringValue(reportData.Metrics, "tt_account_name");
                entity.TtAccountProfileImageUrl = GetStringValue(reportData.Metrics, "tt_account_profile_image_url");

                // Map enums
                var creativeTypeStr = GetStringValue(reportData.Metrics, "creative_type");
                if (!string.IsNullOrEmpty(creativeTypeStr) && Enum.TryParse<TikTok.Enums.CreativeType>(creativeTypeStr, true, out var creativeType))
                {
                    entity.CreativeType = creativeType;
                }

                var ttAccountAuthTypeStr = GetStringValue(reportData.Metrics, "tt_account_authorization_type");
                if (!string.IsNullOrEmpty(ttAccountAuthTypeStr) && Enum.TryParse<TikTok.Enums.TtAccountAuthorizationType>(ttAccountAuthTypeStr, true, out var ttAccountAuthType))
                {
                    entity.TtAccountAuthorizationType = ttAccountAuthType;
                }

                var shopContentTypeStr = GetStringValue(reportData.Metrics, "shop_content_type");
                if (!string.IsNullOrEmpty(shopContentTypeStr) && Enum.TryParse<TikTok.Enums.ShopContentType>(shopContentTypeStr, true, out var shopContentType))
                {
                    entity.ShopContentType = shopContentType;
                }
                var creativeDeliveryStatusStr = GetStringValue(reportData.Metrics, "creative_delivery_status");
                if(!string.IsNullOrEmpty(creativeDeliveryStatusStr) && Enum.TryParse<CreativeDeliveryStatus>(creativeDeliveryStatusStr,out var creativeDeliveryStatus))
                {
                    entity.CreativeDeliveryStatus = creativeDeliveryStatus;
                }
                // Map metrics
                entity.Orders = GetIntValue(reportData.Metrics, "orders");
                entity.GrossRevenue = GetDecimalValue(reportData.Metrics, "gross_revenue");
                entity.ProductImpressions = GetLongValue(reportData.Metrics, "product_impressions");
                entity.ProductClicks = GetLongValue(reportData.Metrics, "product_clicks");
                entity.ProductClickRate = GetDecimalValue(reportData.Metrics, "product_click_rate");
                entity.AdClickRate = GetDecimalValue(reportData.Metrics, "ad_click_rate");
                entity.AdConversionRate = GetDecimalValue(reportData.Metrics, "ad_conversion_rate");
                entity.AdVideoViewRate2s = GetDecimalValue(reportData.Metrics, "ad_video_view_rate_2s");
                entity.AdVideoViewRate6s = GetDecimalValue(reportData.Metrics, "ad_video_view_rate_6s");
                entity.AdVideoViewRateP25 = GetDecimalValue(reportData.Metrics, "ad_video_view_rate_p25");
                entity.AdVideoViewRateP50 = GetDecimalValue(reportData.Metrics, "ad_video_view_rate_p50");
                entity.AdVideoViewRateP75 = GetDecimalValue(reportData.Metrics, "ad_video_view_rate_p75");
                entity.AdVideoViewRateP100 = GetDecimalValue(reportData.Metrics, "ad_video_view_rate_p100");
                entity.ROI = GetDecimalValue(reportData.Metrics, "roi");
                entity.CostPerOrder = GetDecimalValue(reportData.Metrics, "cost_per_order");
                entity.Cost = GetDecimalValue(reportData.Metrics, "cost");
                var currency = GetStringValue(reportData.Metrics, "currency");
                if (!string.IsNullOrEmpty(currency))
                {
                    entity.Currency = currency;
                }
            }

            return entity;
        }
    }
}