using System;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service interface cho việc đồng bộ dữ liệu báo cáo GMV Max Live Campaign
    /// </summary>
    public interface IGmvMaxLiveCampaignSyncService : ITransientDependency
    {
        /// <summary>
        /// Đồng bộ báo cáo GMV Max Live Campaign theo BC ID 
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<GmvMaxLiveCampaignSyncResult> SyncGmvMaxLiveCampaignAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// Đồng bộ báo cáo GMV Max Live Campaign cho tất cả Business Centers với khoảng thời gian mặc định
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<GmvMaxLiveCampaignSyncResult> SyncAllGmvMaxLiveCampaignForAllBcsAsync();
    }

    /// <summary>
    /// Kết quả đồng bộ dữ liệu báo cáo GMV Max Live Campaign
    /// </summary>
    public class GmvMaxLiveCampaignSyncResult : SyncResultBase
    {
        /// <summary>
        /// Số BC đã đồng bộ
        /// </summary>
        public int BcCount { get; set; }

        /// <summary>
        /// Số Campaign đã đồng bộ
        /// </summary>
        public int CampaignCount { get; set; }

        /// <summary>
        /// Số ngày đã đồng bộ
        /// </summary>
        public int DayCount { get; set; }

        /// <summary>
        /// Số Store đã đồng bộ
        /// </summary>
        public int StoreCount { get; set; }

        /// <summary>
        /// Tổng số API calls đã thực hiện
        /// </summary>
        public long ApiCallCount { get; set; }
    }
}
