using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Consts;
using TikTok.DateTimes;
using TikTok.Entities;
using TikTok.Repositories;
using TikTok.TikTokApiClients;
using TikTokBusinessApi;
using TikTokBusinessApi.Models;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu báo cáo GMV Max Live Campaign
    /// </summary>
    public class GmvMaxLiveCampaignSyncService : BaseSyncService, IGmvMaxLiveCampaignSyncService
    {
        private readonly IRawGmvMaxLiveCampaignReportRepository _gmvMaxLiveCampaignReportRepository;
        private readonly IAssetRepository _assetRepository;
        private readonly IRawGmvMaxCampaignsRepository _gmvMaxCampaignsRepository;
        private readonly ITikTokApiClientService _tikTokApiClientService;

        public GmvMaxLiveCampaignSyncService(
            IServiceProvider serviceProvider,
            IRawGmvMaxLiveCampaignReportRepository gmvMaxLiveCampaignReportRepository,
            ILogger<GmvMaxLiveCampaignSyncService> logger,
            IAssetRepository assetRepository,
            IRawGmvMaxCampaignsRepository gmvMaxCampaignsRepository,
            ITikTokApiClientService tikTokApiClientService) : base(serviceProvider, logger)
        {
            _gmvMaxLiveCampaignReportRepository = gmvMaxLiveCampaignReportRepository;
            _assetRepository = assetRepository;
            _gmvMaxCampaignsRepository = gmvMaxCampaignsRepository;
            _tikTokApiClientService = tikTokApiClientService;
        }

        /// <summary>
        /// Đồng bộ báo cáo GMV Max Live Campaign cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxLiveCampaignSyncResult> SyncAllGmvMaxLiveCampaignForAllBcsAsync()
        {
            var result = new GmvMaxLiveCampaignSyncResult
            {
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo GMV Max Live Campaign cho tất cả BC");

                var allBcs = await _businessApplicationCache.GetAllActiveAsync();
                var bcIds = allBcs.Select(x => x.BcId).Distinct().ToList();

                var totalResult = new GmvMaxLiveCampaignSyncResult
                {
                };

                foreach (var bcId in bcIds)
                {
                    var bcResult = await SyncGmvMaxLiveCampaignAsync(bcId);
                    totalResult.TotalSynced += bcResult.TotalSynced;
                    totalResult.NewRecords += bcResult.NewRecords;
                    totalResult.UpdatedRecords += bcResult.UpdatedRecords;
                    totalResult.DayCount += bcResult.DayCount;
                    totalResult.CampaignCount += bcResult.CampaignCount;
                    totalResult.StoreCount += bcResult.StoreCount;
                    totalResult.BcCount++;
                }

                result = totalResult;

                _logger.LogDebug("Hoàn thành đồng bộ báo cáo GMV Max Live Campaign cho tất cả BC. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, BC: {BcCount}, Campaign: {CampaignCount}, Store: {StoreCount}, Ngày: {DayCount}",
                    result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.BcCount, result.CampaignCount, result.StoreCount, result.DayCount);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo GMV Max Live Campaign cho tất cả BC");
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ báo cáo GMV Max Live Campaign: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo GMV Max Live Campaign cho tất cả BC");
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ báo cáo GMV Max Live Campaign theo BC ID và khoảng thời gian
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate">Ngày bắt đầu (tùy chọn)</param>
        /// <param name="endDate">Ngày kết thúc (tùy chọn)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxLiveCampaignSyncResult> SyncGmvMaxLiveCampaignAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var jobStartTime = DateTime.UtcNow;
            var apiCallCount = 0L;
            var result = new GmvMaxLiveCampaignSyncResult
            {
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo GMV Max Live Campaign cho BC: {BcId}", bcId);

                // Lấy danh sách AdvertiserIds từ bcId
                var advertisers = await _assetRepository.GetByBcIdAsync(bcId, assetType: Enums.AssetType.ADVERTISER);
                var advertiserIds = advertisers.Select(x => x.AssetId).ToList();
                if (!advertiserIds.Any())
                {
                    _logger.LogWarning("Không tìm thấy Advertiser nào cho Business Center: {BcId}", bcId);
                    result.ErrorMessage = $"Không tìm thấy Advertiser nào cho Business Center: {bcId}";
                    return result;
                }
                var camps = new List<RawGmvMaxCampaignsEntity>();
                foreach(var ad in advertiserIds)
                {
                    var campaigns = await _gmvMaxCampaignsRepository.GetByAdvertiserIdAsync(ad);
                    camps.AddRange(campaigns);
                }
                if (!camps.Any())
                {
                    _logger.LogWarning("Không tìm thấy Campaign nào cho Business Center: {BcId}", bcId);
                    result.ErrorMessage = $"Không tìm thấy Campaign nào cho Business Center: {bcId}";
                    return result;
                }
                string bcTimezone = DateTimeService.UTC_TIMEZONE;

                var bc = await _businessCenterCache.GetByBcIdAsync(bcId);
                if (bc != null && !string.IsNullOrEmpty(bc.Timezone))
                {
                    bcTimezone = bc.Timezone;
                }
                result.CampaignCount = camps.Count;
                result.StoreCount = camps.Select(x => x.StoreId).Distinct().Count();
                (DateTime StartDate, DateTime EndDate) dateRange;
                // Xác định khoảng thời gian đồng bộ
                if (startDate.HasValue && endDate.HasValue)
                {
                    if(startDate.Value > endDate.Value)
                    {
                        throw new UserFriendlyException("Ngày bắt đầu phải nhỏ hơn ngày kết thúc");
                    }
                    dateRange = (startDate.Value, endDate.Value);
                }
                else
                {
                    dateRange = await GetDateRangeForSyncAsync(bcId, bcTimezone);
                }
                result.DayCount = (int)(dateRange.EndDate - dateRange.StartDate).TotalDays + 1;

                _logger.LogDebug("Đồng bộ báo cáo GMV Max Live Campaign cho BC: {BcId}, Advertisers: {AdvertiserCount}, Campaigns: {CampaignCount}, Store: {StoreCount}, Ngày: {DayCount}",
                    bcId, advertiserIds.Count, result.CampaignCount, result.StoreCount, result.DayCount);

                // Tạo TikTok client từ Singleton
                var tikTokClient = await _tikTokApiClientService.GetOrCreateClientAsync(bcId);

                // Duyệt từng nhà quảng cáo
                foreach (var advertiserId in advertiserIds)
                {
                    var currentDate = dateRange.StartDate.Date;
                    var end = dateRange.EndDate.Date;

                    while (currentDate <= end)
                    {
                        var pageEnd = currentDate.AddDays(30);
                        var daysDifference = (end - currentDate).TotalDays;
                        if (daysDifference < 30)
                        {
                            pageEnd = end;
                        }
                        _logger.LogDebug("Đồng bộ dữ liệu báo cáo GMV Max Live Campaign cho ngày: {Date}", currentDate.ToString("yyyy-MM-dd"));

                        // Count API calls before making the request (regardless of success/failure)
                        apiCallCount += camps.Where(c => c.AdvertiserId == advertiserId).Select(c => c.StoreId).Distinct().Count();

                        var apiResponse = await GetSyncGmvMaxLiveCampaignFromApiAsync(tikTokClient, bcId, advertiserId, currentDate, pageEnd);
                        if (apiResponse != null && apiResponse.Any())
                        {
                            await ProcessReportDataAsync(bcId, advertiserId, bcTimezone, apiResponse, result);
                        }
                        else
                        {
                            _logger.LogDebug("Không có dữ liệu báo cáo GMV Max Live Campaign cho Advertiser: {AdvertiserId}, Ngày: {Date}", advertiserId, currentDate.ToString("yyyy-MM-dd"));
                        }
                        if (pageEnd == end)
                        {
                            break;
                        }
                        currentDate = currentDate.AddDays(29);
                    }
                }

                _logger.LogDebug("Hoàn thành đồng bộ báo cáo GMV Max Live Campaign cho BC: {BcId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}",
                    bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi Business khi đồng bộ báo cáo GMV Max Live Campaign cho BC: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ báo cáo GMV Max Live Campaign: {ex.Message}";
                _logger.LogError(ex, "Lỗi exception khi đồng bộ báo cáo GMV Max Live Campaign cho BC: {BcId}", bcId);
            }

            // Set API call count in result
            result.ApiCallCount = apiCallCount;

            // Log job metrics for this BC
            LogJobMetrics($"SyncGmvMaxLiveCampaign-{bcId}", jobStartTime, apiCallCount, new Dictionary<string, object>
            {
                ["BcId"] = bcId,
                ["TotalSynced"] = result.TotalSynced,
                ["NewRecords"] = result.NewRecords,
                ["UpdatedRecords"] = result.UpdatedRecords,
                ["CampaignCount"] = result.CampaignCount,
                ["StoreCount"] = result.StoreCount,
                ["DayCount"] = result.DayCount
            });

            return result;
        }

        /// <summary>
        /// Lấy khoảng thời gian để đồng bộ dữ liệu
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Khoảng thời gian đồng bộ</returns>
        private async Task<(DateTime StartDate, DateTime EndDate)> GetDateRangeForSyncAsync(string bcId, string timezone)
        {
            // Lấy ngày hiện tại theo timezone của BC
            DateTime currentDateInTimezone = _dateTimeService.GetDateNow(timezone);
            DateTime endDate = currentDateInTimezone;

            //// Lấy dữ liệu báo cáo GMV Max Live Campaign mới nhất
            //var latestReport = await GetLatestReportByBcIdAsync(bcId);

            DateTime startDate = currentDateInTimezone;
            return (startDate, endDate);
        }

        /// <summary>
        /// Lấy báo cáo mới nhất theo BC ID
        /// </summary>
        /// <param name="bcId">BC ID</param>
        /// <returns>Báo cáo mới nhất</returns>
        private async Task<RawGmvMaxLiveCampaignReportEntity?> GetLatestReportByBcIdAsync(string bcId)
        {
            var reports = await _gmvMaxLiveCampaignReportRepository.GetListAsync(
                bcId: bcId,
                sorting: "Date DESC",
                maxResultCount: 1);
            
            return reports.FirstOrDefault();
        }

        /// <summary>
        /// Lấy dữ liệu báo cáo GMV Max Live Campaign từ TikTok API
        /// Lưu ý: Khi lấy báo cáo theo giờ, startDate và endDate phải cùng một ngày
        /// </summary>
        private async Task<List<(GMVMaxReportItem reportItem, string advertiserId, string storeId)>> GetSyncGmvMaxLiveCampaignFromApiAsync(TikTokBusinessApiClient tikTokClient, string bcId, string advertiserId, DateTime startDate, DateTime endDate)
        {

            var records = new List<(GMVMaxReportItem reportItem, string advertiserId, string storeId)>();

            // Lấy danh sách StoreId từ GMV Max Campaigns theo AdvertiserId
            var gmvMaxCampaigns = await _gmvMaxCampaignsRepository.GetByAdvertiserIdAsync(advertiserId);
            var storeIds = gmvMaxCampaigns.Where(x => !string.IsNullOrEmpty(x.StoreId))
                                         .Select(x => x.StoreId)
                                         .Distinct()
                                         .ToList();

            if (!storeIds.Any())
            {
                _logger.LogDebug("Không có Store ID nào cho Advertiser: {AdvertiserId}", advertiserId);
                return records;
            }

            _logger.LogDebug("Tìm thấy {StoreCount} Store ID cho Advertiser: {AdvertiserId}", storeIds.Count, advertiserId);

            // Duyệt từng StoreId vì API chỉ hỗ trợ 1 StoreId mỗi lần gọi
            foreach (var storeId in storeIds)
            {
                var pageRecords = await GetReportFromApiByAdvertiserAndStoreAsync(tikTokClient, advertiserId, storeId, startDate, endDate);
                records.AddRange(pageRecords);
            }

            return records;
        }

        /// <summary>
        /// Lấy báo cáo từ API theo advertiser và store (Campaign report-advertiser)
        /// </summary>
        private async Task<List<(GMVMaxReportItem reportItem, string advertiserId, string storeId)>> GetReportFromApiByAdvertiserAndStoreAsync(TikTokBusinessApiClient tikTokClient, string advertiserId, string storeId, DateTime startDate, DateTime endDate)
        {
            var records = new List<(GMVMaxReportItem reportItem, string advertiserId, string storeId)>();
            var page = 1;
            var pageSize = PAGE_SIZE_SYNC_REPORT;

            while (true)
            {
                var request = new GMVMaxReportRequest
                {
                    AdvertiserId = advertiserId,
                    StoreIds = new List<string> { storeId },
                    StartDate = startDate.ToString("yyyy-MM-dd"),
                    EndDate = endDate.ToString("yyyy-MM-dd"),
                    Dimensions = new List<string> { "campaign_id", "stat_time_day" },
                    Metrics = new List<string>
                    {
                        "currency", "campaign_id", "operation_status", "campaign_name",
                        "schedule_type", "schedule_start_time", "schedule_end_time",
                        "target_roi_budget", "bid_type", "max_delivery_budget", "roas_bid",
                        "cost", "net_cost", "orders", "cost_per_order", "gross_revenue", "roi",
                        "live_views", "cost_per_live_view", "10_second_live_views", 
                        "cost_per_10_second_live_view", "live_follows",
                        "tt_account_name", "tt_account_profile_image_url", "identity_id"
                    },
                    Filtering = new GMVMaxReportFiltering
                    {
                        GMVMaxPromotionTypes = new List<string> { "LIVE" }
                    },
                    Page = page,
                    PageSize = pageSize
                };

                var response = await MeasureApiCallAsync(
                    () => tikTokClient.GMVMax.GetReportAsync(request),
                    "GMVMax.GetReport",
                    ("advertiser_id", advertiserId),
                    ("store_id", storeId),
                    ("report_type", "live_campaign"),
                    ("page", page)
                );

                if (!TikTokApiCodes.IsSuccess(response.Code))
                {
                    throw new BusinessException(response.Code.ToString(), $"Lỗi khi lấy dữ liệu báo cáo GMV Max Live Campaign: {response.Message}");
                }

                if (response?.Data?.List == null || !response.Data.List.Any())
                {
                    break;
                }

                // Thêm AdvertiserId và StoreId vào mỗi item
                foreach (var item in response.Data.List)
                {
                    records.Add((item, advertiserId, storeId));
                }

                // Kiểm tra xem còn trang tiếp theo không
                if (response.Data.PageInfo?.TotalPage <= page)
                {
                    break;
                }

                page++;
            }

            return records;
        }

        /// <summary>
        /// Xử lý dữ liệu báo cáo từ API
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="advertiserId">ID nhà quảng cáo</param>
        /// <param name="bcTimezone">Timezone của Business Center</param>
        /// <param name="reportDataList">Danh sách dữ liệu báo cáo</param>
        /// <param name="result">Kết quả đồng bộ</param>
        private async Task ProcessReportDataAsync(string bcId, string advertiserId, string bcTimezone, List<(GMVMaxReportItem reportItem, string advertiserId, string storeId)> reportDataList, GmvMaxLiveCampaignSyncResult result)
        {
            // sử dụng UnitOfWork để đảm bảo tính toàn vẹn dữ liệu cho mỗi 200 bản ghi
            if (reportDataList == null || !reportDataList.Any())
            {
                _logger.LogDebug("Không có dữ liệu báo cáo GMV Max Live Campaign để xử lý cho {Total} bản ghi", reportDataList?.Count ?? 0);
                return;
            }

            var pageSize = PAGE_SIZE_HANDLE_SAVE_TO_DATABASE;
            var totalPages = (int)Math.Ceiling((double)reportDataList.Count / pageSize);
            _logger.LogDebug("Bắt đầu xử lý {TotalRecords} bản ghi báo cáo GMV Max Live Campaign cho {TotalPages} trang", reportDataList.Count, totalPages);

            for (int page = 0; page < totalPages; page++)
            {
                var pageData = reportDataList.Skip(page * pageSize).Take(pageSize).ToList();
                if (pageData.Any())
                {
                    try
                    {
                        await ProcessPageDataAsync(bcId, bcTimezone, pageData, result);
                    }
                    catch (BusinessException ex)
                    {
                        _logger.LogError(ex, "Lỗi khi xử lý dữ liệu báo cáo GMV Max Live Campaign cho {Total} bản ghi, Trang: {Page}", pageData.Count, page + 1);
                        result.ErrorMessage += ex.Message + Environment.NewLine;
                        result.Code = TikTokApiCodes.PartialSuccess.ToString();
                        continue; // Bỏ qua lỗi và tiếp tục với trang tiếp theo
                    }
                }
            }
        }

        private async Task ProcessPageDataAsync(string bcId, string bcTimezone, List<(GMVMaxReportItem reportItem, string advertiserId, string storeId)> pageData, GmvMaxLiveCampaignSyncResult result)
        {
            // use unit of work
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
            {
                var mappedEntities = await MapListReportDataToEntitiesAsync(bcId, bcTimezone, pageData);

                var existingEntities = await GetExistingEntitiesAsync(bcId, mappedEntities);

                var insertedEntities = new List<RawGmvMaxLiveCampaignReportEntity>();
                var updatedEntities = new List<RawGmvMaxLiveCampaignReportEntity>();

                foreach (var mappedEntity in mappedEntities)
                {
                    // So sánh theo CampaignId và Date với độ chính xác đến giờ
                    var currentEntity = existingEntities.FirstOrDefault(x => x.CampaignId == mappedEntity.CampaignId && x.Date == mappedEntity.Date);
                    if (currentEntity == null)
                    {
                        insertedEntities.Add(mappedEntity);
                        result.NewRecords++;
                        result.TotalSynced++;
                    }
                    else
                    {
                        // Cập nhật nếu có thay đổi
                        if (currentEntity.HasChanged(mappedEntity))
                        {
                            currentEntity.UpdateFrom(mappedEntity);
                            updatedEntities.Add(currentEntity);
                            result.UpdatedRecords++;
                            result.TotalSynced++;
                        }
                    }
                }

                // Thêm các bản ghi mới vào kho dữ liệu
                if (insertedEntities.Any())
                {
                    await _gmvMaxLiveCampaignReportRepository.InsertManyAsync(insertedEntities);
                }
                // Cập nhật các bản ghi đã tồn tại
                if (updatedEntities.Any())
                {
                    await _gmvMaxLiveCampaignReportRepository.UpdateManyAsync(updatedEntities);
                }

                await uow.CompleteAsync();
            }
        }

        /// <summary>
        /// Lấy các entity hiện có trong database
        /// </summary>
        private async Task<List<RawGmvMaxLiveCampaignReportEntity>> GetExistingEntitiesAsync(string bcId, List<RawGmvMaxLiveCampaignReportEntity> mappedEntities)
        {
            if (!mappedEntities.Any())
                return new List<RawGmvMaxLiveCampaignReportEntity>();

            var campaignIds = mappedEntities.Select(x => x.CampaignId).Distinct().ToList();
            var storeIds = mappedEntities.Select(x => x.StoreId).Distinct().ToList();
            var advertiserIds = mappedEntities.Select(x => x.AdvertiserId).Distinct().ToList();

            var minDate = mappedEntities.Min(x => x.Date);
            var maxDate = mappedEntities.Max(x => x.Date);

            var query = await _gmvMaxLiveCampaignReportRepository.GetQueryableAsync();
            return query.Where(x => x.BcId == bcId &&
                                      campaignIds.Contains(x.CampaignId) &&
                                      storeIds.Contains(x.StoreId) &&
                                      advertiserIds.Contains(x.AdvertiserId) &&
                                        x.Date >= minDate && x.Date <= maxDate).ToList();
        }

        private Task<List<RawGmvMaxLiveCampaignReportEntity>> MapListReportDataToEntitiesAsync(string bcId, string bcTimezone, List<(GMVMaxReportItem reportItem, string advertiserId, string storeId)> reportDataList)
        {
            var entities = new List<RawGmvMaxLiveCampaignReportEntity>();
            foreach (var (reportItem, advertiserId, storeId) in reportDataList)
            {
                var entity = MapReportDataToEntity(bcId, bcTimezone, reportItem, advertiserId, storeId);
                if (entity != null)
                {
                    entities.Add(entity);
                }
            }
            return Task.FromResult(entities);
        }

        /// <summary>
        /// Map dữ liệu báo cáo từ API sang entity
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="bcTimezone">Timezone của Business Center</param>
        /// <param name="reportData">Dữ liệu báo cáo</param>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="storeId">ID của Store</param>
        /// <returns>RawGmvMaxLiveCampaignReportEntity</returns>
        private RawGmvMaxLiveCampaignReportEntity? MapReportDataToEntity(string bcId, string bcTimezone, GMVMaxReportItem reportData, string advertiserId, string storeId)
        {
            var dateTimeStr = reportData.Dimensions?.GetValueOrDefault("stat_time_day")?.ToString();

            if (string.IsNullOrEmpty(dateTimeStr) || !DateTime.TryParse(dateTimeStr, out var reportDateTime))
            {
                _logger.LogWarning("Ngày báo cáo GMV Max Live Campaign không hợp lệ: {DateTime} cho BC: {BcId}", dateTimeStr, bcId);
                return null;
            }

            // Convert datetime từ timezone của campaign sang UTC
            var reportDateTimeUtc = _dateTimeService.ConvertToUtc(reportDateTime, bcTimezone);

            var campaignId = reportData.Dimensions?.GetValueOrDefault("campaign_id")?.ToString();
            if (string.IsNullOrEmpty(campaignId))
            {
                _logger.LogWarning("Campaign ID không hợp lệ cho BC: {BcId}", bcId);
                return null;
            }

            var entity = new RawGmvMaxLiveCampaignReportEntity(Guid.NewGuid())
            {
                BcId = bcId,
                AdvertiserId = advertiserId,
                StoreId = storeId,
                CampaignId = campaignId,
                Date = reportDateTimeUtc  // Lưu datetime UTC với ngày
            };

            if (reportData.Metrics != null)
            {
                // Map các metrics từ API response
                entity.CampaignName = GetStringValue(reportData.Metrics, "campaign_name");
                entity.OperationStatus = GetStringValue(reportData.Metrics, "operation_status");
                entity.ScheduleType = GetStringValue(reportData.Metrics, "schedule_type");
                
                // TikTok account related fields (may not be available in API response)
                entity.TtAccountName = GetStringValue(reportData.Metrics, "tt_account_name");
                entity.TtAccountProfileImageUrl = GetStringValue(reportData.Metrics, "tt_account_profile_image_url");
                entity.IdentityId = GetStringValue(reportData.Metrics, "identity_id");

                // Map schedule times
                var scheduleStartTimeStr = GetStringValue(reportData.Metrics, "schedule_start_time");
                if (!string.IsNullOrEmpty(scheduleStartTimeStr) && DateTime.TryParse(scheduleStartTimeStr, out var scheduleStartTime))
                {
                    entity.ScheduleStartTime = scheduleStartTime;
                }

                var scheduleEndTimeStr = GetStringValue(reportData.Metrics, "schedule_end_time");
                if (!string.IsNullOrEmpty(scheduleEndTimeStr) && DateTime.TryParse(scheduleEndTimeStr, out var scheduleEndTime))
                {
                    entity.ScheduleEndTime = scheduleEndTime;
                }

                entity.BidType = GetStringValue(reportData.Metrics, "bid_type");
                entity.TargetRoiBudget = GetDecimalValue(reportData.Metrics, "target_roi_budget");
                entity.MaxDeliveryBudget = GetDecimalValue(reportData.Metrics, "max_delivery_budget");
                entity.RoasBid = GetDecimalValue(reportData.Metrics, "roas_bid");
                entity.Cost = GetDecimalValue(reportData.Metrics, "cost");
                entity.NetCost = GetDecimalValue(reportData.Metrics, "net_cost");
                entity.Orders = GetIntValue(reportData.Metrics, "orders");
                entity.CostPerOrder = GetDecimalValue(reportData.Metrics, "cost_per_order");
                entity.GrossRevenue = GetDecimalValue(reportData.Metrics, "gross_revenue");
                entity.ROI = GetDecimalValue(reportData.Metrics, "roi");
                
                // Live Campaign specific metrics
                entity.LiveViews = GetLongValue(reportData.Metrics, "live_views");
                entity.CostPerLiveView = GetDecimalValue(reportData.Metrics, "cost_per_live_view");
                entity.TenSecondLiveViews = GetLongValue(reportData.Metrics, "ten_second_live_views");
                entity.CostPerTenSecondLiveView = GetDecimalValue(reportData.Metrics, "cost_per_ten_second_live_view");
                entity.LiveFollows = GetIntValue(reportData.Metrics, "live_follows");
                
                entity.Currency = GetStringValue(reportData.Metrics, "currency") ?? "USD";
            }

            return entity;
        }
    }
}
